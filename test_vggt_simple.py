#!/usr/bin/env python3
"""
简化的VGGT测试脚本
"""

import os
import sys
import traceback

# 设置路径 - 确保能找到vggt模块
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)
sys.path.insert(0, os.path.join(current_dir, 'vggt'))

print("=== VGGT模型基础测试 ===")
print(f"当前目录: {current_dir}")
print(f"Python路径: {sys.path[:3]}...")  # 只显示前3个路径

def test_basic_import():
    """基础导入测试"""
    print("\n1. 测试基础模块导入...")
    
    try:
        import torch
        print(f"✓ PyTorch导入成功，版本: {torch.__version__}")
        print(f"✓ CUDA可用: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"✓ GPU: {torch.cuda.get_device_name(0)}")
        
        return True
    except Exception as e:
        print(f"✗ 基础模块导入失败: {e}")
        return False

def test_vggt_import():
    """VGGT模块导入测试"""
    print("\n2. 测试VGGT模块导入...")
    
    try:
        # 逐步导入
        print("  导入aggregator...")
        from vggt.model.models.aggregator import Aggregator
        print("  ✓ Aggregator导入成功")
        
        print("  导入heads...")
        from vggt.model.heads.camera_head import CameraHead
        from vggt.model.heads.dpt_head import DPTHead
        from vggt.model.heads.track_head import TrackHead
        print("  ✓ 所有head模块导入成功")
        
        print("  导入主模型...")
        from vggt.model.models.vggt import VGGT
        print("  ✓ VGGT模型导入成功")
        
        return VGGT
    except Exception as e:
        print(f"  ✗ VGGT模块导入失败: {e}")
        print("  尝试备用导入方法...")
        
        try:
            # 备用导入方法
            sys.path.append('./vggt/vggt')
            from models.vggt import VGGT
            print("  ✓ 备用导入成功")
            return VGGT
        except Exception as e2:
            print(f"  ✗ 备用导入也失败: {e2}")
            traceback.print_exc()
            return None

def test_model_creation(VGGT):
    """模型创建测试"""
    print("\n3. 测试模型创建...")
    
    try:
        # 使用默认参数，避免维度不匹配
        print("  使用默认参数创建模型...")
        model = VGGT()
        print("  ✓ VGGT模型创建成功")
        
        # 计算参数数量
        total_params = sum(p.numel() for p in model.parameters())
        print(f"  ✓ 模型参数数量: {total_params:,}")
        
        # 打印模型基本信息
        print("  模型组件:")
        for name, module in model.named_children():
            print(f"    {name}: {type(module).__name__}")
        
        return model
    except Exception as e:
        print(f"  ✗ 模型创建失败: {e}")
        traceback.print_exc()
        return None

def test_forward_simple(model):
    """简单前向传播测试"""
    print("\n4. 测试简单前向传播...")
    
    try:
        import torch
        
        # 使用默认的图像尺寸518，而不是224
        print("  创建输入张量 (518x518)...")
        images = torch.randn(1, 2, 3, 518, 518)
        print("  ✓ 输入张量创建成功")
        
        model.eval()
        print("  开始前向传播...")
        with torch.no_grad():
            outputs = model(images)
        
        print("  ✓ 前向传播成功")
        print(f"  ✓ 输出键: {list(outputs.keys())}")
        
        # 打印输出形状
        for key, value in outputs.items():
            if hasattr(value, 'shape'):
                print(f"    {key}: {value.shape}")
            else:
                print(f"    {key}: {type(value)}")
        
        return True
    except Exception as e:
        print(f"  ✗ 前向传播失败: {e}")
        traceback.print_exc()
        return False

def main():
    print("开始VGGT模型测试...")
    
    # 测试1: 基础导入
    if not test_basic_import():
        print("\n测试失败：基础模块导入失败")
        return False
    
    # 测试2: VGGT导入
    VGGT = test_vggt_import()
    if VGGT is None:
        print("\n测试失败：VGGT模块导入失败")
        return False
    
    # 测试3: 模型创建
    model = test_model_creation(VGGT)
    if model is None:
        print("\n测试失败：模型创建失败")
        return False
    
    # 测试4: 前向传播
    if not test_forward_simple(model):
        print("\n测试失败：前向传播失败")
        return False
    
    print("\n🎉 所有测试通过！VGGT模型运行正常")
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n=== 测试成功完成 ===")
        else:
            print("\n=== 测试失败 ===")
    except Exception as e:
        print(f"\n=== 测试过程中发生错误: {e} ===")
        traceback.print_exc()