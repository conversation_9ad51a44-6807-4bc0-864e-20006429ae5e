# Copyright (c) Meta Platforms, Inc. and affiliates.
# All rights reserved.
#
# This source code is licensed under the license found in the
# LICENSE file in the root directory of this source tree.

import math
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.checkpoint import checkpoint
from .dpt_head import DPTHead


class StereoHead(nn.Module):
    """
    立体匹配专用头部，真正基于TrackHead思想的相对搜索实现
    
    核心改进：
    1. 移除max_disp约束，采用相对搜索
    2. 迭代累积视差，像TrackHead一样逐步优化
    3. 固定小搜索窗口，内存高效
    4. 保持极线约束
    5. 支持gradient checkpointing降低显存
    """
    
    def __init__(
        self,
        dim_in: int,
        patch_size: int = 14,
        features: int = 128,
        iters: int = 4,
        search_radius: int = 4,  # 像TrackHead一样的搜索半径
        corr_levels: int = 4,
        hidden_size: int = 384,
        down_ratio: int = 2,
        use_checkpoint: bool = True,  # 是否使用gradient checkpointing
    ):
        super().__init__()
        
        self.patch_size = patch_size
        self.iters = iters
        self.search_radius = search_radius  # 替代max_disp
        self.down_ratio = down_ratio
        self.use_checkpoint = use_checkpoint  # 新增checkpointing控制
        
        # StereoAggregator固定输出4层特征 [4, 11, 17, 23]
        # StereoHead使用这4层进行处理，intermediate_layer_idx = [0, 1, 2, 3]
        intermediate_layer_idx = [0, 1, 2, 3]  # 固定使用这4个输出层
        
        print(f"🎯 StereoHead特征提取器: 固定使用 {len(intermediate_layer_idx)} 个层 (索引: {intermediate_layer_idx})")
        
        # 特征提取器（复用DPT架构）
        self.feature_extractor = DPTHead(
            dim_in=dim_in,
            patch_size=patch_size,
            features=features,
            feature_only=True,
            down_ratio=down_ratio,
            pos_embed=False,
            intermediate_layer_idx=intermediate_layer_idx,
        )
        
        # 立体匹配专用的视差预测器（相对搜索版本）
        self.stereo_predictor = RelativeStereoPredictor(
            latent_dim=features,
            search_radius=search_radius,  # 使用搜索半径而不是max_disp
            corr_levels=corr_levels,
            hidden_size=hidden_size,
            iters=iters,
            use_checkpoint=use_checkpoint,  # 传递checkpointing参数
        )
        
    def forward(self, aggregated_tokens_list, images, patch_start_idx=0, frames_chunk_size=8):
        """
        前向传播
        
        Args:
            aggregated_tokens_list: 来自StereoAggregator的多层特征 - List[Tuple[Tensor, Tensor]]
            images: 立体图像对 [B, 2, 3, H, W]
            patch_start_idx: patch token的起始索引
            frames_chunk_size: 处理块大小（兼容DPTHead接口）
            
        Returns:
            Tuple: (disparity, confidence)
                - disparity: 视差图 [B, 2, 1, H, W]
                - confidence: 置信度 [B, 2, 1, H, W]
        """
        B, S, _, H, W = images.shape
        assert S == 2, "需要立体图像对 (左图, 右图)"
        
        # 转换StereoAggregator输出格式为DPTHead期望格式
        dpt_tokens_list = []
        for left_feat, right_feat in aggregated_tokens_list:
            stereo_feat = torch.stack([left_feat, right_feat], dim=1)  # [B, 2, P, C]
            dpt_tokens_list.append(stereo_feat)
        
        # 提取多尺度特征
        feature_maps = self.feature_extractor(
            dpt_tokens_list, 
            images=images, 
            patch_start_idx=patch_start_idx,
            frames_chunk_size=frames_chunk_size
        )  # [B, 2, C, H//down_ratio, W//down_ratio]
        
        # 执行相对搜索立体匹配
        disparity_sequence, confidence_sequence = self.stereo_predictor(
            feature_maps=feature_maps,
        )
        
        # 上采样到原始分辨率
        final_disparity = self._upsample_disparity(
            disparity_sequence[-1], 
            target_size=(H, W)
        )
        final_confidence = self._upsample_confidence(
            confidence_sequence[-1], 
            target_size=(H, W)
        )
        
        # 为左右两个视图创建输出（当前只实现左视图）
        disparity_output = torch.stack([final_disparity[:, 0], final_disparity[:, 0]], dim=1)  # [B, 2, 1, H, W]
        confidence_output = torch.stack([final_confidence[:, 0], final_confidence[:, 0]], dim=1)  # [B, 2, 1, H, W]
        
        return disparity_output, confidence_output
    
    def _upsample_disparity(self, disparity, target_size):
        """上采样视差图并调整尺度"""
        upsampled = F.interpolate(
            disparity, size=target_size, 
            mode='bilinear', align_corners=True
        )
        return upsampled * self.down_ratio  # 恢复原始视差尺度
    
    def _upsample_confidence(self, confidence, target_size):
        """上采样置信度图"""
        return F.interpolate(
            confidence, size=target_size, 
            mode='bilinear', align_corners=True
        )


class RelativeStereoPredictor(nn.Module):
    """
    基于TrackHead思想的相对搜索立体匹配预测器
    
    核心思想：
    1. 初始视差为0
    2. 每次迭代在当前视差 ± radius 范围内搜索
    3. 更新视差 = 当前视差 + 搜索到的相对偏移
    4. 通过多次迭代累积到最终视差值
    """
    
    def __init__(
        self,
        latent_dim: int = 128,
        search_radius: int = 4,  # 搜索半径，类似TrackHead
        corr_levels: int = 4,
        hidden_size: int = 384,
        iters: int = 6,
        use_checkpoint: bool = True,  # 是否使用gradient checkpointing
    ):
        super().__init__()
        
        self.latent_dim = latent_dim
        self.search_radius = search_radius
        self.iters = iters
        self.use_checkpoint = use_checkpoint  # 新增checkpointing控制
        
        # 搜索窗口大小：固定为 2*radius+1
        self.search_window = 2 * search_radius + 1
        
        # 相关性处理网络（固定输入维度）
        self.corr_mlp = nn.Sequential(
            nn.Linear(self.search_window, hidden_size),
            nn.GELU(),
            nn.Linear(hidden_size, latent_dim),
        )
        
        # 视差嵌入维度
        self.disp_emb_dim = latent_dim // 2
        
        # 视差更新网络
        self.disparity_updater = StereoUpdateFormer(
            input_dim=latent_dim + latent_dim + self.disp_emb_dim + 1,  # 相关性+特征+视差嵌入+视差值
            hidden_size=hidden_size,
            output_dim=latent_dim + 1,  # 特征更新 + 视差增量
        )
        
        # 层归一化
        self.fmap_norm = nn.LayerNorm(latent_dim)
        self.ffeat_norm = nn.GroupNorm(1, latent_dim)
        
        # 特征更新器
        self.feature_updater = nn.Sequential(
            nn.Linear(latent_dim, latent_dim), 
            nn.GELU()
        )
        
        # 置信度预测器
        self.confidence_predictor = nn.Sequential(
            nn.Linear(latent_dim, hidden_size // 4),
            nn.GELU(),
            nn.Linear(hidden_size // 4, 1),
            nn.Sigmoid()
        )
        
    def forward(self, feature_maps):
        """
        执行相对搜索立体匹配预测
        
        Args:
            feature_maps: [B, 2, C, H, W] - 左右图特征
            
        Returns:
            disparity_sequence: 迭代视差预测序列
            confidence_sequence: 迭代置信度预测序列
        """
        B, _, C, H, W = feature_maps.shape
        device = feature_maps.device
        
        # 应用层归一化
        left_fmap = self.fmap_norm(feature_maps[:, 0].permute(0, 2, 3, 1)).permute(0, 3, 1, 2)
        right_fmap = self.fmap_norm(feature_maps[:, 1].permute(0, 2, 3, 1)).permute(0, 3, 1, 2)
        
        # 生成密集查询点（左图每个像素）
        y_coords, x_coords = torch.meshgrid(
            torch.arange(H, device=device, dtype=torch.float),
            torch.arange(W, device=device, dtype=torch.float),
            indexing='ij'
        )
        query_coords = torch.stack([x_coords, y_coords], dim=-1)  # [H, W, 2]
        query_coords = query_coords.view(-1, 2).unsqueeze(0).expand(B, -1, -1)  # [B, H*W, 2]
        
        # 初始化视差为0（关键差异：不预设范围）
        disparity = torch.zeros(B, H * W, 1, device=device)
        
        # 从左图提取初始特征
        left_features = self._sample_features(left_fmap, query_coords)  # [B, H*W, C]
        pixel_features = left_features.clone()
        
        # 创建相对搜索相关性计算器
        relative_corr = RelativeCorrelationBlock(
            left_fmap, right_fmap, self.search_radius, H, W
        )
        
        disparity_sequence = []
        confidence_sequence = []
        
        # 迭代优化（TrackHead风格）
        for iter_idx in range(self.iters):
            # 分离梯度
            disparity = disparity.detach()
            
            if self.use_checkpoint and self.training:
                # 使用gradient checkpointing包装整个迭代步骤以节省显存
                def iteration_step(disp_input, pixel_feats_input):
                    # 相对搜索：在当前视差 ± radius 范围内搜索
                    correlations = relative_corr.compute_relative_correlation(
                        query_coords, disp_input.squeeze(-1)
                    )  # [B, H*W, search_window]
                    
                    # 处理相关性特征
                    corr_features = self.corr_mlp(correlations)  # [B, H*W, latent_dim]
                    
                    # 视差嵌入
                    disp_embedding = self._get_disparity_embedding(disp_input)  # [B, H*W, disp_emb_dim]
                    
                    # 拼接所有特征
                    transformer_input = torch.cat([
                        corr_features,           # 相关性特征
                        pixel_feats_input,       # 像素特征
                        disp_embedding,          # 视差嵌入
                        disp_input,              # 当前视差值
                    ], dim=-1)
                    
                    # 通过更新网络预测增量
                    delta_output = self.disparity_updater(
                        transformer_input.view(B, H, W, -1)
                    )  # [B, H, W, latent_dim+1]
                    
                    delta_features = delta_output[..., :-1].view(B, H * W, -1)  # [B, H*W, latent_dim]
                    delta_disparity = delta_output[..., -1:].view(B, H * W, 1)   # [B, H*W, 1]
                    
                    # 更新像素特征
                    updated_pixel_features = pixel_feats_input + self.feature_updater(
                        self.ffeat_norm(delta_features.view(B * H * W, -1))
                    ).view(B, H * W, -1)
                    
                    # 累积视差更新（关键：累积而不是约束）
                    updated_disparity = disp_input + delta_disparity
                    
                    # 预测置信度
                    confidence = self.confidence_predictor(updated_pixel_features)  # [B, H*W, 1]
                    
                    return updated_disparity, updated_pixel_features, confidence
                
                disparity, pixel_features, confidence = checkpoint(
                    iteration_step, disparity, pixel_features, use_reentrant=False
                )
            else:
                # 正常执行（无checkpointing）
                # 相对搜索：在当前视差 ± radius 范围内搜索
                correlations = relative_corr.compute_relative_correlation(
                    query_coords, disparity.squeeze(-1)
                )  # [B, H*W, search_window]
                
                # 处理相关性特征
                corr_features = self.corr_mlp(correlations)  # [B, H*W, latent_dim]
                
                # 视差嵌入
                disp_embedding = self._get_disparity_embedding(disparity)  # [B, H*W, disp_emb_dim]
                
                # 拼接所有特征
                transformer_input = torch.cat([
                    corr_features,           # 相关性特征
                    pixel_features,          # 像素特征
                    disp_embedding,          # 视差嵌入
                    disparity,               # 当前视差值
                ], dim=-1)
                
                # 通过更新网络预测增量
                delta_output = self.disparity_updater(
                    transformer_input.view(B, H, W, -1)
                )  # [B, H, W, latent_dim+1]
                
                delta_features = delta_output[..., :-1].view(B, H * W, -1)  # [B, H*W, latent_dim]
                delta_disparity = delta_output[..., -1:].view(B, H * W, 1)   # [B, H*W, 1]
                
                # 更新像素特征
                pixel_features = pixel_features + self.feature_updater(
                    self.ffeat_norm(delta_features.view(B * H * W, -1))
                ).view(B, H * W, -1)
                
                # 累积视差更新（关键：累积而不是约束）
                disparity = disparity + delta_disparity
                
                # 预测置信度
                confidence = self.confidence_predictor(pixel_features)  # [B, H*W, 1]
            
            # 重塑为图像格式
            disp_map = disparity.view(B, 1, H, W)
            conf_map = confidence.view(B, 1, H, W)
            
            disparity_sequence.append(disp_map)
            confidence_sequence.append(conf_map)
        
        return disparity_sequence, confidence_sequence
    
    def _sample_features(self, feature_map, coords):
        """从特征图中采样特征"""
        B, C, H, W = feature_map.shape
        N = coords.shape[1]
        
        # 归一化坐标到[-1, 1]
        norm_coords = coords.clone()
        norm_coords[:, :, 0] = 2 * coords[:, :, 0] / (W - 1) - 1
        norm_coords[:, :, 1] = 2 * coords[:, :, 1] / (H - 1) - 1
        
        # 使用grid_sample采样
        sampled = F.grid_sample(
            feature_map, 
            norm_coords.view(B, 1, N, 2),
            mode='bilinear', 
            align_corners=True
        )  # [B, C, 1, N]
        
        return sampled.squeeze(2).transpose(1, 2)  # [B, N, C]
    
    def _get_disparity_embedding(self, disparity):
        """生成视差的位置编码"""
        B, N, _ = disparity.shape
        
        # 正弦位置编码
        pe = torch.zeros(B, N, self.disp_emb_dim, device=disparity.device)
        div_term = torch.exp(
            torch.arange(0, self.disp_emb_dim, 2, device=disparity.device) * 
            -(math.log(10000.0) / self.disp_emb_dim)
        )
        
        pe[:, :, 0::2] = torch.sin(disparity.squeeze(-1).unsqueeze(-1) * div_term)
        pe[:, :, 1::2] = torch.cos(disparity.squeeze(-1).unsqueeze(-1) * div_term)
        
        return pe


class RelativeCorrelationBlock:
    """
    相对搜索的极线相关性计算，真正类似TrackHead的CorrBlock
    
    关键差异：
    1. 固定小搜索窗口（2*radius+1）
    2. 相对于当前视差进行搜索
    3. 内存消耗恒定且小
    """
    
    def __init__(self, left_fmap, right_fmap, search_radius, H, W):
        self.left_fmap = left_fmap    # [B, C, H, W]
        self.right_fmap = right_fmap  # [B, C, H, W]
        self.search_radius = search_radius
        self.H = H
        self.W = W
        self.B, self.C = left_fmap.shape[:2]
        
        # 预计算搜索偏移（类似TrackHead的delta）
        # 但只在x方向（极线约束）
        offsets = torch.arange(-search_radius, search_radius + 1, 
                              device=left_fmap.device, dtype=torch.float)
        self.search_offsets = offsets  # [-radius, ..., +radius]
        
    def compute_relative_correlation(self, query_coords, current_disp):
        """
        计算相对搜索的相关性（类似TrackHead的corr_sample）
        
        Args:
            query_coords: 查询坐标 [B, N, 2] (左图像素坐标)
            current_disp: 当前视差 [B, N] 
            
        Returns:
            correlations: [B, N, search_window] - 相对搜索范围内的相关性
        """
        B, N = current_disp.shape
        device = query_coords.device
        
        # 从左图采样特征
        left_features = self._sample_features(self.left_fmap, query_coords)  # [B, N, C]
        
        correlations = []
        
        # 在当前视差周围搜索（类似TrackHead在当前位置周围搜索）
        for offset in self.search_offsets:
            # 计算右图采样坐标：当前视差 + 相对偏移
            right_coords = query_coords.clone()
            right_coords[:, :, 0] = query_coords[:, :, 0] - (current_disp + offset)  # x -= (disp + offset)
            
            # 边界检查
            valid_mask = (right_coords[:, :, 0] >= 0) & (right_coords[:, :, 0] < self.W)
            
            # 从右图采样特征
            right_features = self._sample_features(self.right_fmap, right_coords)  # [B, N, C]
            
            # 计算相关性（归一化点积）
            correlation = torch.sum(left_features * right_features, dim=-1) / math.sqrt(self.C)  # [B, N]
            
            # 无效位置设为低相关性
            correlation[~valid_mask] = -1.0
            
            correlations.append(correlation)
        
        return torch.stack(correlations, dim=-1)  # [B, N, search_window]
    
    def _sample_features(self, feature_map, coords):
        """特征采样"""
        B, C, H, W = feature_map.shape
        N = coords.shape[1]
        
        # 归一化坐标
        norm_coords = coords.clone()
        norm_coords[:, :, 0] = 2 * coords[:, :, 0] / (W - 1) - 1
        norm_coords[:, :, 1] = 2 * coords[:, :, 1] / (H - 1) - 1
        
        # 边界处理
        norm_coords = torch.clamp(norm_coords, -1, 1)
        
        sampled = F.grid_sample(
            feature_map, 
            norm_coords.view(B, 1, N, 2),
            mode='bilinear', 
            align_corners=True,
            padding_mode='border'
        )
        
        return sampled.squeeze(2).transpose(1, 2)  # [B, N, C]


class StereoUpdateFormer(nn.Module):
    """
    专门用于立体匹配的更新网络
    借鉴TrackHead的UpdateFormer，但针对2D图像的空间结构优化
    """
    
    def __init__(self, input_dim, hidden_size, output_dim, num_heads=8, depth=4):
        super().__init__()
        
        self.input_norm = nn.LayerNorm(input_dim)
        self.input_transform = nn.Linear(input_dim, hidden_size)
        
        # 2D空间注意力层（适应图像结构）
        self.spatial_blocks = nn.ModuleList([
            SpatialAttentionBlock(hidden_size, num_heads)
            for _ in range(depth)
        ])
        
        self.output_norm = nn.LayerNorm(hidden_size)
        self.output_head = nn.Linear(hidden_size, output_dim)
        
        self.initialize_weights()
        
    def initialize_weights(self):
        """初始化权重"""
        def _basic_init(module):
            if isinstance(module, nn.Linear):
                torch.nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
        
        self.apply(_basic_init)
        # 小初始化最后一层，确保训练稳定
        torch.nn.init.trunc_normal_(self.output_head.weight, std=0.001)
        
    def forward(self, x):
        """
        Args:
            x: [B, H, W, input_dim]
        Returns:
            [B, H, W, output_dim]
        """
        # 输入处理
        x = self.input_norm(x)
        x = self.input_transform(x)  # [B, H, W, hidden_size]
        
        residual = x
        
        # 空间注意力处理
        for block in self.spatial_blocks:
            x = block(x)
        
        x = x + residual
        
        # 输出处理
        x = self.output_norm(x)
        x = self.output_head(x)
        
        return x


class SpatialAttentionBlock(nn.Module):
    """2D空间注意力块，内存高效版本"""
    
    def __init__(self, dim, num_heads):
        super().__init__()
        self.dim = dim
        self.num_heads = num_heads
        
        # 使用简化的处理方式，避免大规模的全局注意力
        self.norm1 = nn.LayerNorm(dim)
        self.norm2 = nn.LayerNorm(dim)
        
        # 替换注意力为高效的卷积操作
        self.spatial_conv = nn.Sequential(
            nn.Conv2d(dim, dim, kernel_size=3, padding=1, groups=dim),  # 深度卷积
            nn.GELU(),
            nn.Conv2d(dim, dim, kernel_size=1),  # 点卷积
        )
        
        self.mlp = nn.Sequential(
            nn.Linear(dim, dim * 4),
            nn.GELU(),
            nn.Linear(dim * 4, dim)
        )
        
    def forward(self, x):
        B, H, W, C = x.shape
        
        # 空间卷积代替注意力
        shortcut = x
        x = self.norm1(x)
        
        # 转换为卷积格式 [B, C, H, W]
        x_conv = x.permute(0, 3, 1, 2)
        x_conv = self.spatial_conv(x_conv)
        # 转换回原格式 [B, H, W, C]
        x = x_conv.permute(0, 2, 3, 1)
        
        x = x + shortcut
        
        # MLP
        shortcut = x
        x = self.norm2(x)
        x = self.mlp(x)
        x = x + shortcut
        
        return x 