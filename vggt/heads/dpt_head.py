import os
from typing import List, Dict, Tuple, Union, Optional

import torch
import torch.nn as nn
import torch.nn.functional as F
from .head_act import activate_head
from .utils import create_uv_grid, position_grid_to_embed


class DPTHead(nn.Module):
    """
    DPT  Head for dense prediction tasks.

    This implementation follows the architecture described in "Vision Transformers for Dense Prediction"
    (https://arxiv.org/abs/2103.13413). The DPT head processes features from a vision transformer
    backbone and produces dense predictions by fusing multi-scale features.

    Args:
        dim_in (int): Input dimension (channels).
        patch_size (int, optional): Patch size. Default is 14.
        output_dim (int, optional): Number of output channels. Default is 4.
        activation (str, optional): Activation type. Default is "inv_log".
        conf_activation (str, optional): Confidence activation type. Default is "expp1".
        features (int, optional): Feature channels for intermediate representations. Default is 256.
        out_channels (List[int], optional): Output channels for each intermediate layer.
        intermediate_layer_idx (List[int], optional): Indices of layers from aggregated tokens used for DPT.
        pos_embed (bool, optional): Whether to use positional embedding. Default is True.
        feature_only (bool, optional): If True, return features only without the last several layers and activation head. Default is False.
        down_ratio (int, optional): Downscaling factor for the output resolution. Default is 1.
    """

    def __init__(
        self,
        dim_in: int,
        patch_size: int = 14,
        output_dim: int = 4,
        activation: str = "inv_log",
        conf_activation: str = "expp1",
        features: int = 256,
        out_channels: List[int] = [256, 512, 1024, 1024],
        intermediate_layer_idx: List[int] = [4, 11, 17, 23],
        pos_embed: bool = True,
        feature_only: bool = False,
        down_ratio: int = 1,
        # multiscale_features: bool = False,  # 注释掉未使用的参数
    ) -> None:
        super(DPTHead, self).__init__()
        self.patch_size = patch_size
        self.activation = activation
        self.conf_activation = conf_activation
        self.pos_embed = pos_embed
        self.feature_only = feature_only
        self.down_ratio = down_ratio
        # self.multiscale_features = multiscale_features  # 注释掉未使用的属性
        self.intermediate_layer_idx = intermediate_layer_idx

        self.norm = nn.LayerNorm(dim_in)

        # Projection layers for each output channel from tokens.
        # projects: List[nn.Conv2d]，每个卷积层将dim_in维特征映射到对应的out_channels维
        self.projects = nn.ModuleList(
            [
                nn.Conv2d(
                    in_channels=dim_in,
                    out_channels=oc,
                    kernel_size=1,
                    stride=1,
                    padding=0,
                )
                for oc in out_channels
            ]
        )

        # Resize layers for upsampling feature maps.
        # resize_layers: List[nn.Module]，用于上采样特征图到统一尺寸
        self.resize_layers = nn.ModuleList(
            [
                nn.ConvTranspose2d(
                    in_channels=out_channels[0], out_channels=out_channels[0], kernel_size=4, stride=4, padding=0
                ),
                nn.ConvTranspose2d(
                    in_channels=out_channels[1], out_channels=out_channels[1], kernel_size=2, stride=2, padding=0
                ),
                nn.Identity(),
                nn.Conv2d(
                    in_channels=out_channels[3], out_channels=out_channels[3], kernel_size=3, stride=2, padding=1
                ),
            ]
        )

        self.scratch = _make_scratch(
            out_channels,
            features,
            expand=False,
        )

        # Attach additional modules to scratch.
        self.scratch.refinenet1 = _make_fusion_block(features)
        self.scratch.refinenet2 = _make_fusion_block(features)
        self.scratch.refinenet3 = _make_fusion_block(features)
        self.scratch.refinenet4 = _make_fusion_block(features, has_residual=False)

    
    def forward(
        self,
        aggregated_tokens_list: List[torch.Tensor],
        images: torch.Tensor,
        patch_start_idx: int,
    ):
        """
        Implementation of the forward pass through the DPT head.

        This method processes a specific chunk of frames from the sequence.

        Args:
            aggregated_tokens_list (List[Tensor]): List of token tensors from different transformer layers.
                Each element has shape [B, S*P, 2*C]
            images (Tensor): Input images with shape [B, S, 3, H, W].
            patch_start_idx (int): Starting index for patch tokens.

        Returns:
            Tensor or Tuple[Tensor, Tensor]: Feature maps or (predictions, confidence).
                - Feature maps: [B, S, C, H, W]
                - Predictions: [B, S, 1, H, W]
                - Confidence: [B, S, 1, H, W]
        """

        # images: [B, S_chunk, 3, H, W] 其中S_chunk是当前处理的帧数
        B, S_chunk, _, H, W = images.shape

        # 计算patch的高和宽
        patch_h, patch_w = H // self.patch_size, W // self.patch_size

        out = []
        dpt_idx = 0

        # 处理每个中间层的特征
        for layer_idx in self.intermediate_layer_idx:
            # x: [B, S_chunk*P, 2*C] 其中P是token数量
            x = aggregated_tokens_list[layer_idx][:, :, patch_start_idx:]

            # Since we always process exactly 2 frames, no need for frame selection

            # x: [B * S_chunk, P, 2*C]
            x = x.view(B * S_chunk, -1, x.shape[-1])

            # 归一化
            # x: [B * S_chunk, P, 2*C]
            x = self.norm(x)

            # 重塑为2D特征图
            # x: [B * S_chunk, 2*C, patch_h, patch_w]
            x = x.permute(0, 2, 1).reshape((x.shape[0], x.shape[-1], patch_h, patch_w))

            # 投影到目标通道数
            # x: [B * S_chunk, out_channels[dpt_idx], patch_h, patch_w]
            x = self.projects[dpt_idx](x)
            if self.pos_embed:
                # 添加位置嵌入
                x = self._apply_pos_embed(x, W, H)
            # 上采样到统一尺寸
            # x: [B * S_chunk, out_channels[dpt_idx], patch_h*stride, patch_w*stride]
            x = self.resize_layers[dpt_idx](x)

            # 添加到输出列表
            out.append(x)
            dpt_idx += 1

        # Check if we need to return multi-scale features
        # if self.multiscale_features:
        # 生成多尺度特征金字塔
        multiscale_out = self.scratch_forward_multiscale(out)

        # 将每个尺度的特征插值到对应的目标分辨率
        pyramid_features = []
        target_scales = [4, 8, 16, 32]  # 对应1/4, 1/8, 1/16, 1/32分辨率

        for i, (scale_features, target_scale) in enumerate(zip(multiscale_out, target_scales)):
            target_h = int(H / target_scale)
            target_w = int(W / target_scale)

            # 插值到目标分辨率
            scale_features = custom_interpolate(
                scale_features,
                size=(target_h, target_w),
                mode="bilinear",
                align_corners=True,
            )

            if self.pos_embed:
                # 添加位置嵌入
                scale_features = self._apply_pos_embed(scale_features, W, H)

            # 重塑为最终输出格式: [B, S_chunk, features, target_h, target_w]
            scale_features = scale_features.view(B, S_chunk, *scale_features.shape[1:])
            pyramid_features.append(scale_features)

        return pyramid_features

    def _apply_pos_embed(self, x: torch.Tensor, W: int, H: int, ratio: float = 0.1) -> torch.Tensor:
        """
        Apply positional embedding to tensor x.
        
        Args:
            x: 输入特征图 [B, C, H, W]
            W: 原始图像宽度
            H: 原始图像高度
            ratio: 位置嵌入缩放因子
            
        Returns:
            带位置嵌入的特征图 [B, C, H, W]
        """
        patch_w = x.shape[-1]
        patch_h = x.shape[-2]
        # pos_embed: [patch_w, patch_h, 2]
        pos_embed = create_uv_grid(patch_w, patch_h, aspect_ratio=W / H, dtype=x.dtype, device=x.device)
        # pos_embed: [C, patch_h, patch_w]
        pos_embed = position_grid_to_embed(pos_embed, x.shape[1])
        # pos_embed: [C, patch_h, patch_w]
        pos_embed = pos_embed * ratio
        # pos_embed: [1, C, patch_h, patch_w]
        pos_embed = pos_embed.permute(2, 0, 1)[None].expand(x.shape[0], -1, -1, -1)
        # 返回: [B, C, patch_h, patch_w]
        return x + pos_embed

    def scratch_forward_multiscale(self, features: List[torch.Tensor]) -> List[torch.Tensor]:
        """
        Forward pass through the fusion blocks to generate multi-scale features.
        Similar to Monster Plus DPTHead_decoder implementation.

        Args:
            features (List[Tensor]): List of feature maps from different layers.
                Each element has shape [B*S_chunk, channels, height, width]

        Returns:
            List[Tensor]: Multi-scale feature maps [path_1, path_2, path_3, path_4]
                - path_1: [B*S_chunk, 256, h0, w0] (1/4 scale)
                - path_2: [B*S_chunk, 256, h1, w1] (1/8 scale)
                - path_3: [B*S_chunk, 256, h2, w2] (1/16 scale)
                - path_4: [B*S_chunk, 256, h3, w3] (1/32 scale)
        """
        layer_1, layer_2, layer_3, layer_4 = features

        # 通过1x1卷积调整通道数到统一的256维
        layer_1_rn = self.scratch.layer1_rn(layer_1)  # [B*S_chunk, 256, h0, w0]
        layer_2_rn = self.scratch.layer2_rn(layer_2)  # [B*S_chunk, 256, h1, w1]
        layer_3_rn = self.scratch.layer3_rn(layer_3)  # [B*S_chunk, 256, h2, w2]
        layer_4_rn = self.scratch.layer4_rn(layer_4)  # [B*S_chunk, 256, h3, w3]

        # 自上而下融合特征，类似Monster Plus的实现
        # path_4: 最小尺度特征 (1/32)
        path_4 = self.scratch.refinenet4(layer_4_rn, size=layer_4_rn.shape[2:])

        # path_3: 上采样path_4并与layer_3_rn融合 (1/16)
        up_path_4 = custom_interpolate(path_4, size=layer_3_rn.shape[2:], mode='bilinear', align_corners=True)
        path_3 = self.scratch.refinenet3(up_path_4, layer_3_rn, size=layer_3_rn.shape[2:])

        # path_2: 上采样path_3并与layer_2_rn融合 (1/8)
        up_path_3 = custom_interpolate(path_3, size=layer_2_rn.shape[2:], mode='bilinear', align_corners=True)
        path_2 = self.scratch.refinenet2(up_path_3, layer_2_rn, size=layer_2_rn.shape[2:])

        # path_1: 上采样path_2并与layer_1_rn融合 (1/4)
        up_path_2 = custom_interpolate(path_2, size=layer_1_rn.shape[2:], mode='bilinear', align_corners=True)
        path_1 = self.scratch.refinenet1(up_path_2, layer_1_rn, size=layer_1_rn.shape[2:])

        return [path_1, path_2, path_3, path_4]


################################################################################
# Modules
################################################################################


def _make_fusion_block(features: int, size: Optional[int] = None, has_residual: bool = True, groups: int = 1) -> nn.Module:
    return FeatureFusionBlock(
        features,
        nn.ReLU(inplace=True),
        deconv=False,
        bn=False,
        expand=False,
        align_corners=True,
        size=size,
        has_residual=has_residual,
        groups=groups,
    )


def _make_scratch(in_shape: List[int], out_shape: int, groups: int = 1, expand: bool = False) -> nn.Module:
    scratch = nn.Module()
    out_shape1 = out_shape
    out_shape2 = out_shape
    out_shape3 = out_shape
    if len(in_shape) >= 4:
        out_shape4 = out_shape

    if expand:
        out_shape1 = out_shape
        out_shape2 = out_shape * 2
        out_shape3 = out_shape * 4
        if len(in_shape) >= 4:
            out_shape4 = out_shape * 8

    scratch.layer1_rn = nn.Conv2d(
        in_shape[0], out_shape1, kernel_size=3, stride=1, padding=1, bias=False, groups=groups
    )
    scratch.layer2_rn = nn.Conv2d(
        in_shape[1], out_shape2, kernel_size=3, stride=1, padding=1, bias=False, groups=groups
    )
    scratch.layer3_rn = nn.Conv2d(
        in_shape[2], out_shape3, kernel_size=3, stride=1, padding=1, bias=False, groups=groups
    )
    if len(in_shape) >= 4:
        scratch.layer4_rn = nn.Conv2d(
            in_shape[3], out_shape4, kernel_size=3, stride=1, padding=1, bias=False, groups=groups
        )
    return scratch


class ResidualConvUnit(nn.Module):
    """Residual convolution module."""

    def __init__(self, features, activation, bn, groups=1):
        """Init.

        Args:
            features (int): number of features
        """
        super().__init__()

        self.bn = bn
        self.groups = groups
        self.conv1 = nn.Conv2d(features, features, kernel_size=3, stride=1, padding=1, bias=True, groups=self.groups)
        self.conv2 = nn.Conv2d(features, features, kernel_size=3, stride=1, padding=1, bias=True, groups=self.groups)

        self.norm1 = None
        self.norm2 = None

        self.activation = activation
        self.skip_add = nn.quantized.FloatFunctional()

    def forward(self, x):
        """Forward pass.

        Args:
            x (tensor): input with shape [B, C, H, W]

        Returns:
            tensor: output with shape [B, C, H, W]
        """

        out = self.activation(x)
        out = self.conv1(out)
        if self.norm1 is not None:
            out = self.norm1(out)

        out = self.activation(out)
        out = self.conv2(out)
        if self.norm2 is not None:
            out = self.norm2(out)

        return self.skip_add.add(out, x)


class FeatureFusionBlock(nn.Module):
    """Feature fusion block."""

    def __init__(
        self,
        features,
        activation,
        deconv=False,
        bn=False,
        expand=False,
        align_corners=True,
        size=None,
        has_residual=True,
        groups=1,
    ):
        """Init.

        Args:
            features (int): number of features
        """
        super(FeatureFusionBlock, self).__init__()

        self.deconv = deconv
        self.align_corners = align_corners
        self.groups = groups
        self.expand = expand
        out_features = features
        if self.expand == True:
            out_features = features // 2

        self.out_conv = nn.Conv2d(
            features, out_features, kernel_size=1, stride=1, padding=0, bias=True, groups=self.groups
        )

        if has_residual:
            self.resConfUnit1 = ResidualConvUnit(features, activation, bn, groups=self.groups)

        self.has_residual = has_residual
        self.resConfUnit2 = ResidualConvUnit(features, activation, bn, groups=self.groups)

        self.skip_add = nn.quantized.FloatFunctional()
        self.size = size

    def forward(self, *xs, size=None):
        """Forward pass.

        Args:
            *xs: Variable number of input tensors, typically 1 or 2 tensors with shape [B, C, H, W]
            size: Target size for interpolation

        Returns:
            tensor: output with shape [B, C, H*2, W*2] (typically upsampled by factor of 2)
        """
        # xs[0]: 主输入特征 [B, C, H, W]
        output = xs[0]

        # 如果有残差连接且提供了第二个输入
        if self.has_residual:
            # xs[1]: 残差输入特征 [B, C, H, W]
            res = self.resConfUnit1(xs[1])
            # 将主输入和残差输入相加
            output = self.skip_add.add(output, res)

        # 应用第二个残差卷积单元
        output = self.resConfUnit2(output)

        # 插值到目标尺寸
        if (size is None) and (self.size is None):
            modifier = {"scale_factor": 2}
        elif size is None:
            modifier = {"size": self.size}
        else:
            modifier = {"size": size}

        # 上采样特征图
        output = custom_interpolate(output, **modifier, mode="bilinear", align_corners=self.align_corners)
        # 1x1卷积调整通道数
        output = self.out_conv(output)

        return output


def custom_interpolate(
    x: torch.Tensor,
    size: Optional[Tuple[int, int]] = None,
    scale_factor: Optional[float] = None,
    mode: str = "bilinear",
    align_corners: bool = True,
) -> torch.Tensor:
    """
    Custom interpolate to avoid INT_MAX issues in nn.functional.interpolate.
    
    Args:
        x: 输入张量 [B, C, H, W]
        size: 目标尺寸 (H, W)
        scale_factor: 缩放因子
        mode: 插值模式
        align_corners: 是否对齐角点
        
    Returns:
        插值后的张量 [B, C, H', W']
    """
    if size is None:
        if scale_factor is None:
            raise ValueError("Either size or scale_factor must be provided")
        size = (int(x.shape[-2] * scale_factor), int(x.shape[-1] * scale_factor))

    INT_MAX = 1610612736

    input_elements = size[0] * size[1] * x.shape[0] * x.shape[1]

    if input_elements > INT_MAX:
        chunks = torch.chunk(x, chunks=(input_elements // INT_MAX) + 1, dim=0)
        interpolated_chunks = [
            nn.functional.interpolate(chunk, size=size, mode=mode, align_corners=align_corners) for chunk in chunks
        ]
        x = torch.cat(interpolated_chunks, dim=0)
        return x.contiguous()
    else:
        return nn.functional.interpolate(x, size=size, mode=mode, align_corners=align_corners)


class MultiScaleDPTHead(nn.Module):
    """
    Multi-Scale DPT Head for extracting feature pyramids at multiple resolutions.

    This head extracts features at 1/4, 1/8, 1/16, and 1/32 resolutions from VGGT tokens
    for downstream stereo matching tasks, similar to Monster Plus architecture.

    Args:
        dim_in (int): Input dimension (channels) from VGGT tokens.
        patch_size (int): Patch size used in VGGT. Default is 14.
        features (int): Feature channels for intermediate representations. Default is 256.
        out_channels (List[int]): Output channels for each scale [1/4, 1/8, 1/16, 1/32].
        intermediate_layer_idx (List[int]): Indices of layers from aggregated tokens used for feature extraction.
        pos_embed (bool): Whether to use positional embedding. Default is True.
    """

    def __init__(
        self,
        dim_in: int,
        patch_size: int = 14,
        features: int = 256,
        out_channels: List[int] = [96, 192, 384, 768],  # Similar to Monster Plus vitb config
        intermediate_layer_idx: List[int] = [4, 11, 17, 23],
        pos_embed: bool = True,
    ) -> None:
        super(MultiScaleDPTHead, self).__init__()

        self.patch_size = patch_size
        self.pos_embed = pos_embed
        self.intermediate_layer_idx = intermediate_layer_idx
        self.out_channels = out_channels

        self.norm = nn.LayerNorm(dim_in)

        # Projection layers for each scale
        self.projects = nn.ModuleList([
            nn.Conv2d(
                in_channels=dim_in,
                out_channels=oc,
                kernel_size=1,
                stride=1,
                padding=0,
            )
            for oc in out_channels
        ])

        # Downsample layers for creating feature pyramid
        self.downsample_layers = nn.ModuleList([
            nn.Identity(),  # 1/4 scale - no downsampling
            nn.Conv2d(out_channels[0], out_channels[1], kernel_size=3, stride=2, padding=1),  # 1/4 -> 1/8
            nn.Conv2d(out_channels[1], out_channels[2], kernel_size=3, stride=2, padding=1),  # 1/8 -> 1/16
            nn.Conv2d(out_channels[2], out_channels[3], kernel_size=3, stride=2, padding=1),  # 1/16 -> 1/32
        ])

        # Scale processors for refining features at each scale
        self.scale_processors = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(oc, oc, kernel_size=3, stride=1, padding=1),
                nn.ReLU(inplace=True),
                nn.Conv2d(oc, oc, kernel_size=3, stride=1, padding=1),
                nn.ReLU(inplace=True),
            )
            for oc in out_channels
        ])

    def forward(
        self,
        aggregated_tokens_list: List[torch.Tensor],
        images: torch.Tensor,
        patch_start_idx: int,
    ) -> List[torch.Tensor]:
        """
        Forward pass to extract multi-scale features.

        Args:
            aggregated_tokens_list (List[Tensor]): List of token tensors from different transformer layers.
                Each element has shape [B, S*P, 2*C]
            images (Tensor): Input images with shape [B, S, 3, H, W].
            patch_start_idx (int): Starting index for patch tokens.

        Returns:
            List[Tensor]: Multi-scale feature pyramid with 4 scales:
                - Scale 0 (1/4): [B, S, 96, H/4, W/4]
                - Scale 1 (1/8): [B, S, 192, H/8, W/8]
                - Scale 2 (1/16): [B, S, 384, H/16, W/16]
                - Scale 3 (1/32): [B, S, 768, H/32, W/32]
        """

        B, S_chunk, _, H, W = images.shape
        patch_h, patch_w = H // self.patch_size, W // self.patch_size

        # Process features from the first (lowest) layer only for simplicity
        layer_idx = self.intermediate_layer_idx[0]
        x = aggregated_tokens_list[layer_idx][:, :, patch_start_idx:]

        # Select frames if processing a chunk
        if frames_start_idx is not None and frames_end_idx is not None:
            x = x[:, frames_start_idx:frames_end_idx]

        # Reshape to process tokens: [B * S_chunk, P, 2*C]
        x = x.view(B * S_chunk, -1, x.shape[-1])

        # Normalize and reshape to spatial format
        x = self.norm(x)  # [B * S_chunk, P, 2*C]
        x = x.permute(0, 2, 1).reshape((x.shape[0], x.shape[-1], patch_h, patch_w))  # [B * S_chunk, 2*C, patch_h, patch_w]

        # Project to base feature dimension
        base_features = self.projects[0](x)  # [B * S_chunk, out_channels[0], patch_h, patch_w]

        # Apply positional embedding if enabled
        if self.pos_embed:
            base_features = self._apply_pos_embed(base_features, W, H)

        # Upsample base features to 1/4 resolution (target resolution for first scale)
        target_h, target_w = H // 4, W // 4
        feat_1_4 = custom_interpolate(
            base_features,
            size=(target_h, target_w),
            mode="bilinear",
            align_corners=True
        )  # [B * S_chunk, out_channels[0], H/4, W/4]

        # Process 1/4 scale features
        feat_1_4 = self.scale_processors[0](feat_1_4)

        # Create feature pyramid by progressive downsampling
        pyramid_features = []
        current_feat = feat_1_4

        for scale_idx in range(len(self.out_channels)):
            if scale_idx == 0:
                # 1/4 scale - already processed
                processed_feat = current_feat
            else:
                # Downsample and project to next scale
                current_feat = self.downsample_layers[scale_idx](current_feat)
                processed_feat = self.scale_processors[scale_idx](current_feat)

            # Reshape to final output format: [B, S_chunk, C, H, W]
            final_feat = processed_feat.view(B, S_chunk, *processed_feat.shape[1:])
            pyramid_features.append(final_feat)

            # Update current features for next iteration
            current_feat = processed_feat

        return pyramid_features

    def _apply_pos_embed(self, x: torch.Tensor, W: int, H: int, ratio: float = 0.1) -> torch.Tensor:
        """
        Apply positional embedding to tensor x.

        Args:
            x: Input feature map [B, C, H, W]
            W: Original image width
            H: Original image height
            ratio: Position embedding scaling factor

        Returns:
            Feature map with positional embedding [B, C, H, W]
        """
        patch_w = x.shape[-1]
        patch_h = x.shape[-2]
        pos_embed = create_uv_grid(patch_w, patch_h, aspect_ratio=W / H, dtype=x.dtype, device=x.device)
        pos_embed = position_grid_to_embed(pos_embed, x.shape[1])
        pos_embed = pos_embed * ratio
        pos_embed = pos_embed.permute(2, 0, 1)[None].expand(x.shape[0], -1, -1, -1)
        return x + pos_embed
